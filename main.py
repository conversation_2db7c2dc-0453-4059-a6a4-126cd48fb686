#!/usr/bin/env python3
"""
Invoice extractor – flexibel filinsamling.
1. Om du anger specifika PDF‑filer som argument, parser de.
2. Annars: plural alla PDF‑filer i den mapp där skriptet körs.
3. Extraherar:
     - Bestellnr
     - Filnamn (utan .pdf)
     - Summa netto (€)
     - Start‑ och slutdatum (Leistungszeitraum)
     - LEB‑datum (LEB‑Erfassung am)
4. Bygger pandas DataFrame, visar med ace_tools, och sparar som Excel.
"""

import re
import argparse
from pathlib import Path
import datetime as dt
import pdfplumber
import pandas as pd
from ace_tools import display_dataframe_to_user

# Regex‑mönster
RX_BESTELL  = re.compile(r"Bestell\s+Nr\.\s*:\s*([\d/]+)")
RX_PERIOD   = re.compile(r"Leistungszeitraum:\s*(\d{2}\.\d{2}\.\d{4})\s*bis\s*(\d{2}\.\d{2}\.\d{4})")
RX_LEB_DATE = re.compile(r"LEB\-Erfassung\s+am:\s*(\d{2}\.\d{2}\.\d{4})")
RX_NETTO    = re.compile(r"Summe\s+Netto\s+€\s*([\d\.,]+)")

def parse_pdf(path: Path) -> dict:
    text = pdfplumber.open(path).pages[0].extract_text()
    def find(rx):
        m = rx.search(text or "")
        return m.group(1) if m else ""

    bestell = find(RX_BESTELL)
    period = find(RX_PERIOD).split(" bis ")
    start, end = (period if len(period)==2 else ("",""))
    leb     = find(RX_LEB_DATE)
    net_raw = find(RX_NETTO)
    netto   = float(net_raw.replace(".", "").replace(",", ".")) if net_raw else 0.0

    return {
        "Bestellnr":     bestell,
        "Filnamn":       path.stem,
        "Summa netto (€)": netto,
        "Startdatum":    pd.to_datetime(start, dayfirst=True).date() if start else pd.NaT,
        "Slutdatum":     pd.to_datetime(end,   dayfirst=True).date() if end   else pd.NaT,
        "LEB‑datum":     pd.to_datetime(leb,   dayfirst=True).date() if leb   else pd.NaT,
        "Format":        f"Bestellnr. {bestell} ({path.stem})"
    }

def main():
    p = argparse.ArgumentParser(description="Extrahera data från PDF‑fakturor")
    p.add_argument(
        "pdf_files", nargs="*",
        help="PDF-filer att parsa (om ej angivna, tar alla *.pdf i cwd)"
    )
    args = p.parse_args()

    if args.pdf_files:
        paths = [Path(f) for f in args.pdf_files]
    else:
        paths = list(Path.cwd().glob("*.pdf"))

    if not paths:
        print("Inga PDF-filer hittades.")
        return

    rows = [parse_pdf(p) for p in paths]
    df   = pd.DataFrame(rows)

    # Sammanfattning
    total = df["Summa netto (€)"].sum()
    overall_start = df["Startdatum"].min()
    overall_end   = df["Slutdatum"].max()
    summary = pd.Series({
        "Bestellnr":     "TOTAL",
        "Filnamn":       "",
        "Summa netto (€)": total,
        "Startdatum":    overall_start,
        "Slutdatum":     overall_end,
        "LEB‑datum":     pd.NaT,
        "Format":        f"Total: {total:,.2f} € ({overall_start} – {overall_end})"
    })
    df = pd.concat([df, summary.to_frame().T], ignore_index=True)

    # Visa i UI
    display_dataframe_to_user("Invoice summary", df)

    # Spara Excel
    ts = dt.datetime.now().strftime("%Y%m%d_%H%M%S")
    outfile = Path.cwd() / f"invoice_summary_{ts}.xlsx"
    df.to_excel(outfile, index=False)
    print(f"Excel sparad till {outfile}")

if __name__ == "__main__":
    main()
